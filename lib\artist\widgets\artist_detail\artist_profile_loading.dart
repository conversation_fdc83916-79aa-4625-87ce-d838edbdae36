import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/appbar/profile_menu_indicator.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail/profile_header_loading.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/settings/widgets/settings_screen.dart';

class ArtistProfileLoading extends StatelessWidget {
  const ArtistProfileLoading({super.key, required this.artist});

  final ArtistPartialData artist;

  @override
  Widget build(BuildContext context) {
    final int userId = LocalUserService.userId ?? 0;
    final bool isOwnProfile = artist.id == userId;

    return Scaffold(
      appBar: PmAppBar(
        automaticallyImplyLeading: true,
        backgroundColor: context.colors.lightColor,
        titleText: '@${artist.nicename}',
        useLogo: false,
        actions: [
          ProfileMenuIndicator(
            onTap: () {
              if (!isOwnProfile) return;

              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: ListView(
          children: [
            Container(
              color: context.colors.lightColor,
              padding: const EdgeInsets.only(
                top: 7.0,
                right: ScreenStyleConfig.horizontalPadding,
                bottom: 65.0,
                left: ScreenStyleConfig.horizontalPadding,
              ),
              child: ProfileHeaderLoading(artist: artist),
            ),
            Column(
              children: [
                for (int i = 0; i < 5; i++) ...[
                  const SizedBox(height: 12.0),
                  Container(
                    width: double.infinity,
                    height: 220.0,
                    padding: const EdgeInsets.symmetric(
                      horizontal: ScreenStyleConfig.horizontalPadding,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(
                        PhotoStyleConfig.borderRadius,
                      ),
                      child: Container(
                        color: context.isDarkMode
                            ? AppColorsCache.dark().baseColorAlt.withAlpha(95)
                            : AppColorsCache.light().baseColorAlt,
                      ),
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 12.0),
          ],
        ),
      ),
    );
  }
}
